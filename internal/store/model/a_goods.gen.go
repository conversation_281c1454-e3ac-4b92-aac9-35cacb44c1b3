// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameAGood = "a_goods"

// AGood mapped from table <a_goods>
type AGood struct {
	ID              int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID          string `gorm:"column:game_id" json:"game_id"`
	GoodsID         string `gorm:"column:goods_id;not null" json:"goods_id"`
	GoodsName       string `gorm:"column:goods_name;not null;comment:商品名称" json:"goods_name"`                                                                       // 商品名称
	Money           int32  `gorm:"column:money;not null;comment:金额 (分)" json:"money"`                                                                               // 金额 (分)
	Description     string `gorm:"column:description;not null;comment:描述" json:"description"`                                                                       // 描述
	PayType         string `gorm:"column:pay_type;not null;comment:支付类型 1:米大师 2:iOS H5支付  3:Google  4: iOS APP苹果支付 5: 抖音小游戏安卓虚拟支付 6: 抖音小游戏iOS钻石支付" json:"pay_type"` // 支付类型 1:米大师 2:iOS H5支付  3:Google  4: iOS APP苹果支付 5: 抖音小游戏安卓虚拟支付 6: 抖音小游戏iOS钻石支付
	WechatProductID string `gorm:"column:wechat_product_id;not null;comment:微信商品ID (米大师)" json:"wechat_product_id"`                                                 // 微信商品ID (米大师)
	Remark          string `gorm:"column:remark;not null;comment:备注" json:"remark"`                                                                                 // 备注
	CreatorID       string `gorm:"column:creator_id;not null" json:"creator_id"`
	CreatedAt       int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt       int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted       bool   `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName AGood's table name
func (*AGood) TableName() string {
	return TableNameAGood
}
