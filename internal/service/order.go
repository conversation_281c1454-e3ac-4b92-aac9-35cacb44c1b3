package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"

	"git.panlonggame.com/bkxplatform/admin-console/internal/https"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/go-resty/resty/v2"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

var (
	_orderOnce    sync.Once
	_orderService *OrderService
)

type OrderService struct {
	client *resty.Client
	https  *https.DouyinHttpService
}

func SingletonOrderService() *OrderService {
	_orderOnce.Do(func() {
		_orderService = &OrderService{
			client: resty.New(),
			https:  https.SingletonDouyinHttpService(),
		}
	})
	return _orderService
}

// GetVerifyMoney 获取金额并校验
func (s *OrderService) GetVerifyMoney(ctx context.Context, gameID, id string, money int32) (int32, error) {
	goods := store.QueryDB().AGood
	goodsCtx := goods.WithContext(ctx)
	goodsInfo, err := goodsCtx.Where(goods.GameID.Eq(gameID)).Where(goods.GoodsID.Eq(id)).Where(goods.IsDeleted.Zero()).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.ErrorfCtx(ctx, "【GetVerifyMoney】 goods not found, gameID: %s, goodsID: %s, money: %d", gameID, id, money)
		return 0, constants.ErrOrderGoodsInfo
	} else if err != nil {
		return 0, err
	}
	if money == 0 || goodsInfo.Money != money {
		logger.Logger.ErrorfCtx(ctx, "【GetVerifyMoney】 money not match, gameID: %s, goodsID: %s, money: %d, goodsInfo.Money: %d", gameID, id, money, goodsInfo.Money)
		return 0, constants.ErrOrderMoney
	}

	// 记录商品验证成功
	logger.Logger.InfofCtx(ctx, "【GetVerifyMoney】 商品验证成功, gameID: %s, goodsID: %s, money: %d", gameID, id, goodsInfo.Money)
	return goodsInfo.Money, nil
}

// CreateOrder 创建订单
func (s *OrderService) CreateOrder(ctx context.Context, req *bean.CreateOrderReq) (*bean.Order, error) {
	goods := store.QueryDB().AGood
	goodsCtx := goods.WithContext(ctx)
	goodsInfo, err := goodsCtx.Where(goods.GameID.Eq(req.GameID)).Where(goods.GoodsID.Eq(req.GoodsID)).Where(goods.IsDeleted.Zero()).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, constants.ErrOrderGoodsInfo
	} else if err != nil {
		return nil, err
	}

	order := store.QueryDB().AOrder
	orderCtx := order.WithContext(ctx)
	orderModel := &model.AOrder{
		UserID:  req.UserID,
		GameID:  req.GameID,
		OrderID: util.UUIDWithoutHyphens(),
		GoodsID: req.GoodsID,
		Money:   req.Money,
		// PlatformType: req.PlatformType,
		// PayType:      req.PayType,
		Status:           constants.PaymentOrderCreate,
		Extra:            req.Extra,
		ShipmentCallback: req.ShipmentCallback,
	}
	//if req.PayType == constants.PayTypeAndroidDouyinPay || req.PayType == constants.PayTypeIOSDouyinPay {
	//	if err := s.handleDouyinPay(orderModel, orderModel.Money); err != nil {
	//		return nil, err
	//	}
	//}
	err = orderCtx.Create(orderModel)
	if err != nil {
		return nil, err
	}
	orderRes := &bean.Order{}
	err = copier.Copy(&orderRes, orderModel)
	if err != nil {
		return nil, err
	}
	orderRes.GoodsName = goodsInfo.GoodsName
	return orderRes, nil
}

func (s *OrderService) GetDouyinGameCurrency(money int32) (int32, error) {
	// 抖音小游戏安卓虚拟支付 money 的单位为分
	gameCurrency := money / 10 // buyQuantity / 游戏币单价 = 限定价格等级
	// 检查money是否在价格等级中
	if money < 100 {
		return 0, constants.ErrDouyinMoneyMinimum // The minimum payment amount for Douyin has not been reached
	}
	if !util.Contains(constants.DouyinPriceLevels, money) {
		return 0, constants.ErrDouyinMoneyRule
	}
	return gameCurrency, nil
}

// GetOrderDetail 获取订单
func (s *OrderService) GetOrderDetail(ctx context.Context, orderID string) (*bean.Order, error) {
	order := store.QueryDB().AOrder
	orderCtx := order.WithContext(ctx)
	orderInfo, err := orderCtx.Where(order.OrderID.Eq(orderID)).Where(order.IsDeleted.Zero()).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, constants.ErrOrderNotFound
	}
	if err != nil {
		return nil, err
	}

	goods := store.QueryDB().AGood
	goodsCtx := goods.WithContext(ctx)
	goodsInfo, err := goodsCtx.
		Where(goods.GameID.Eq(orderInfo.GameID)).
		Where(goods.GoodsID.Eq(orderInfo.GoodsID)).
		Where(goods.IsDeleted.Zero()).First()
	if err != nil {
		logger.Logger.Errorf("OrderService GetOrderDetail goods id not found")
		return nil, err
	}

	res := &bean.Order{}
	err = copier.Copy(res, orderInfo)
	if err != nil {
		return nil, err
	}
	res.GoodsName = goodsInfo.GoodsName
	res.WechatProductID = goodsInfo.WechatProductID

	payTypeSlice := make([]int32, 0)
	err = json.Unmarshal([]byte(goodsInfo.PayType), &payTypeSlice)
	if err != nil {
		return nil, err
	}
	res.GoodsPayTypes = payTypeSlice
	// 校验payTypeSlice是否包含orderInfo.PayType
	//if !util.InSlice(payTypeSlice, orderInfo.PayType) {
	//	return nil, constants.ErrOrderPayType
	//}
	return res, nil
}

// AddProductShipmentList adds a list of products
func (s *OrderService) AddProductShipmentList(ctx context.Context, orderID string) error {
	logger.Logger.Debugf("[debug] OrderService AddProductShipmentList param :%s", orderID)
	logger.Logger.Debugf("[debug] OrderService AddProductShipmentList list key :%s", constants.SystemProductShipmentList)
	_, err := redis.Redis().RPush(ctx, constants.SystemProductShipmentList, orderID).Result()
	if err != nil {
		logger.Logger.Errorf("OrderService AddProductShipmentList redis add list err: %s", err.Error())
		return err
	}
	return nil
}

// GetProductShipmentOrderAndCallbackURL to get product
func (s *OrderService) GetProductShipmentOrderAndCallbackURL(ctx context.Context, orderID string) (*model.AOrder, string, error) {
	order := store.QueryDB().AOrder
	orderCtx := order.WithContext(ctx)
	// 查询微信支付成功的订单
	orderInfo, err := orderCtx.WithContext(ctx).
		Where(order.Status.Eq(constants.PaymentWechatPaySuccess)).
		Where(order.OrderID.Eq(orderID)).
		Where(order.IsDeleted.Zero()).First()
	if err != nil {
		logger.Logger.Errorf("OrderService CallProductShipment getting order err: %s", err.Error())
		return nil, "", err
	}
	//if orderInfo.Status != constants.PaymentWechatPaySuccess {
	//	logger.Logger.Errorf("OrderService CallProductShipment order status is: %d", orderInfo.Status)
	//	return nil, "", err
	//}
	game := store.QueryDB().MGame
	gameCtx := game.WithContext(ctx)
	gameInfo, err := gameCtx.Where(game.GameID.Eq(orderInfo.GameID)).First()
	if err != nil {
		logger.Logger.Errorf("OrderService CallProductShipment find game err: %s", err.Error())
		return nil, "", err
	}
	if gameInfo.PayCallback == "" {
		logger.Logger.Errorf("OrderService CallProductShipment pay callback is empty")
		return nil, "", nil
	}

	return orderInfo, gameInfo.PayCallback, nil
}

//func (s *OrderService) CallWithRetry(ctx context.Context, orderByte []byte) error {
//	// maxAttempts := len(constants.RetryDelays)
//
//	// for attempt := 0; attempt < maxAttempts; attempt++ {
//
//	client := asynq.NewClient(asynq.RedisClientOpt{
//		Addr:     config.GlobConfig.RedisJob.Hosts[0],
//		DB:       config.GlobConfig.RedisJob.DB,
//		Username: config.GlobConfig.RedisJob.UserName,
//		Password: config.GlobConfig.RedisJob.Password,
//		PoolSize: config.GlobConfig.RedisJob.PoolSize,
//	})
//	_, err := client.Enqueue(asynq.NewTask("product_shipment_order", orderByte))
//	if err != nil {
//		return err
//	}
//
//	//if err == nil {
//	//	return s.UpdateOrderStatus(ctx, orderReq.Order.OrderID, map[string]interface{}{"status": constants.PaymentProductShipmentSuccess})
//	//} else if err != nil {
//	//	logger.Logger.Errorf("CallWithRetry run err: 尝试 %d：调用失败，gameid: %s，userid: %s，orderid: %s，err: %s，将在一段时间后重新尝试...",
//	//		attempt+1, orderReq.Order.GameID, orderReq.Order.UserID, orderReq.Order.OrderID, err.Error())
//	//
//	//	// _, err = sdasdasd.SubmitByDelay(asynq.NewTask("product_shipment_order", orderByte), constants.RetryDelays[attempt])
//	//	if err != nil {
//	//		logger.Logger.Errorf("CallWithRetry submit delay, time: %d ms，尝试 %d: 调用失败，gameid: %s，userid: %s，orderid: %s，err: %s，将在一段时间后重新尝试...",
//	//			constants.RetryDelays[attempt], attempt+1, orderReq.Order.GameID, orderReq.Order.UserID, orderReq.Order.OrderID, err.Error())
//	//	}
//	//	if attempt == maxAttempts-1 { // 达到最大调用次数，终止调用
//	//		logger.Logger.Errorf("CallWithRetry reached maximum attempts err: 尝试 %d：调用失败，gameid: %s，userid: %s，orderid: %s，CallWithRetry 没有成功返回结果, 达到最大尝试次数，停止尝试",
//	//			maxAttempts-1, orderReq.Order.GameID, orderReq.Order.UserID, orderReq.Order.OrderID)
//	//		logger.Logger.ErrorWithFiled(map[string]interface{}{
//	//			"order_id": orderReq.Order.OrderID,
//	//			"game_id":  orderReq.Order.GameID,
//	//			"extra":    "连续重试完成后，调用失败",
//	//		}, "充值回调异常")
//	//		return s.UpdateOrderStatus(ctx, orderReq.Order.OrderID, map[string]interface{}{"status": constants.PaymentProductShipmentFail})
//	//	}
//	//	continue
//	//}
//	// logger.Logger.Errorf("CallWithRetry errors and correctness are not caught, check the code")
//	return nil
//}

//func (s *OrderService) CallWithRetry(ctx context.Context, order *model.AOrder, callbackURL string) error {
//	maxAttempts := len(constants.RetryDelays)
//
//	taskOrderReq := &bean.OrderReq{}
//	err := copier.Copy(taskOrderReq.Order, order)
//	if err != nil {
//		return err
//	}
//	taskOrderReq.CallbackURL = callbackURL // copy 之后赋值url防止被覆盖
//
//	orderByte, err := json.Marshal(taskOrderReq)
//	if err != nil {
//		return err
//	}
//
//	for attempt := 0; attempt < maxAttempts; attempt++ {
//		_, err = task.Submit(asynq.NewTask("product_shipment_order", orderByte))
//		if err != nil {
//			return err
//		}
//		if err == nil {
//			return s.UpdateOrderStatus(ctx, taskOrderReq.Order.OrderID, map[string]interface{}{"status": constants.PaymentProductShipmentSuccess})
//		} else if err != nil {
//			logger.Logger.Errorf("CallWithRetry run err: 尝试 %d：调用失败，gameid: %s，userid: %s，orderid: %s，err: %s，将在一段时间后重新尝试...",
//				attempt+1, order.GameID, order.UserID, order.OrderID, err.Error())
//
//			_, err = task.SubmitByDelay(asynq.NewTask("product_shipment_order", orderByte), constants.RetryDelays[attempt])
//			if err != nil {
//				logger.Logger.Errorf("CallWithRetry submit delay, time: %d ms，尝试 %d: 调用失败，gameid: %s，userid: %s，orderid: %s，err: %s，将在一段时间后重新尝试...",
//					constants.RetryDelays[attempt], attempt+1, order.GameID, order.UserID, order.OrderID, err.Error())
//			}
//			if attempt == maxAttempts-1 { // 达到最大调用次数，终止调用
//				logger.Logger.Errorf("CallWithRetry reached maximum attempts err: 尝试 %d：调用失败，gameid: %s，userid: %s，orderid: %s，CallWithRetry 没有成功返回结果, 达到最大尝试次数，停止尝试",
//					maxAttempts-1, order.GameID, order.UserID, order.OrderID)
//				logger.Logger.ErrorWithFiled(map[string]interface{}{
//					"order_id": order.OrderID,
//					"game_id":  order.GameID,
//					"extra":    "连续重试完成后，调用失败",
//				}, "充值回调异常")
//				return s.UpdateOrderStatus(ctx, order.OrderID, map[string]interface{}{"status": constants.PaymentProductShipmentFail})
//			}
//			continue
//		}
//	}
//	logger.Logger.Errorf("CallWithRetry errors and correctness are not caught, check the code")
//	return nil
//}

// CallProductShipment 调用产品发货接口
func (s *OrderService) CallProductShipment(ctx context.Context, req *bean.OrderReq) (*bean.ProductShipmentRes, error) {
	if req.CallbackURL == "" || req.Order == nil {
		return nil, fmt.Errorf("OrderService CallProductShipment callbackURL is empty, req order info: %+v", req)
	}

	content, err := json.Marshal(req.Order)
	if err != nil {
		return nil, err
	}
	logger.Logger.Infof("OrderService CallProductShipment gameid: %s, content: %s", req.Order.GameID, string(content))

	// timestamp to string
	timestamp := time.Now().Unix()
	timestampStr := strconv.FormatInt(timestamp, 10)

	secretKey := fmt.Sprintf(constants.SystemSecret, req.Order.GameID)
	secretVal, err := redis.Get(ctx, secretKey)
	if err != nil {
		logger.Logger.Errorf("OrderService CallProductShipment: redis get secret, game id: %s, err: %s", req.Order.GameID, err.Error())
		return nil, err
	}
	sign := middleware.GenSign(req.Order.GameID, secretVal, timestampStr, content)

	result := &bean.ProductShipmentRes{}
	resp, err := s.client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader("game_id", req.Order.GameID).
		SetHeader("timestamp", timestampStr).
		SetHeader("sign", sign).
		SetBody(content).
		Post(req.CallbackURL)
	if err != nil {
		logger.Logger.Errorf("OrderService CallProductShipment: 调用失败, 请检查接口 err: %s", err.Error())
		return nil, err
	}
	if resp.StatusCode() != http.StatusOK {
		// logger.Logger.Errorf("OrderService CallProductShipment: 获取状态码异常，状态码: %d", resp.StatusCode())
		return nil, fmt.Errorf("OrderService CallProductShipment: 获取状态码异常，状态码: %d", resp.StatusCode())
	}
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		logger.Logger.Errorf("OrderService CallProductShipment: Unmarshal err: %s", err.Error())
		return nil, err
	}
	result.OrderID = req.Order.OrderID
	if result.Code != 0 {
		// logger.Logger.Errorf("OrderService CallProductShipment: 成功调用，但是产品方返回Code码异常 code: %d， msg: %s", result.Code, result.Msg)
		return result, fmt.Errorf("OrderService CallProductShipment: 成功调用，但是产品方返回Code码异常 gameid: %s, userid: %s, orderid: %s, code: %d，msg: %s",
			req.Order.GameID, req.Order.UserID, req.Order.OrderID, result.Code, result.Msg)
	}

	return result, nil
}

// GetGoodsDetail returns the list of
func (s *OrderService) GetGoodsDetail(ctx context.Context, goodsID string) (*model.AGood, error) {
	goods := store.QueryDB().AGood
	goodsCtx := goods.WithContext(ctx)
	goodsInfo, err := goodsCtx.Where(goods.GoodsID.Eq(goodsID)).Where(goods.IsDeleted.Zero()).First()
	if err != nil {
		logger.Logger.Errorf("OrderService GetGoodsDetail goods id not found")
		return nil, err
	}
	return goodsInfo, nil
}

// GetGoodsDetailByGameIDAndGoodsID 根据游戏ID和商品ID获取商品详情
func (s *OrderService) GetGoodsDetailByGameIDAndGoodsID(ctx context.Context, gameID, goodsID string) (*model.AGood, error) {
	goods := store.QueryDB().AGood
	goodsCtx := goods.WithContext(ctx)
	goodsInfo, err := goodsCtx.Where(goods.GameID.Eq(gameID)).Where(goods.GoodsID.Eq(goodsID)).Where(goods.IsDeleted.Zero()).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.Errorf("OrderService GetGoodsDetailByGameIDAndGoodsID goods not found, gameID: %s, goodsID: %s", gameID, goodsID)
			return nil, constants.ErrOrderGoodsInfo
		}
		logger.Logger.Errorf("OrderService GetGoodsDetailByGameIDAndGoodsID query error: %s", err.Error())
		return nil, err
	}
	return goodsInfo, nil
}

// UpdateOrderStatus updates the order status
func (s *OrderService) UpdateOrderStatus(ctx context.Context, orderID string, updateOrder map[string]interface{}) error {
	order := store.QueryDB().AOrder
	orderCtx := order.WithContext(ctx)
	_, err := orderCtx.Where(order.OrderID.Eq(orderID)).Where(order.IsDeleted.Zero()).Updates(updateOrder)
	if err != nil {
		logger.Logger.Errorf("OrderService UpdateOrderStatus err: %s", err.Error())
		return err
	}
	return nil
}

// UpdateOrderStatusBatches updates the order status
func (s *OrderService) UpdateOrderStatusBatches(ctx context.Context, orderIDs []string, updateOrder map[string]interface{}) error {
	order := store.QueryDB().AOrder
	orderCtx := order.WithContext(ctx)
	_, err := orderCtx.Where(order.OrderID.In(orderIDs...)).Where(order.IsDeleted.Zero()).Updates(updateOrder)
	if err != nil {
		logger.Logger.Errorf("OrderService UpdateOrderStatusBatches err: %s", err.Error())
		return err
	}
	return nil
}

// IsFirstPay 是否是首次充值
func (s *OrderService) IsFirstPay(ctx context.Context, userID string) bool {
	statusValues := []int32{
		constants.PaymentWechatPaySuccess,
		constants.PaymentProductShipmentSuccess,
		constants.PaymentProductShipmentFail, // 发货失败的前提微信依然是支付成功的
	}

	order := store.QueryDB().AOrder
	orderCtx := order.WithContext(ctx)
	orders, err := orderCtx.
		Select(order.ID).
		Where(order.UserID.Eq(userID)).
		Where(order.Status.In(statusValues...)).
		Where(order.IsDeleted.Zero()).
		Limit(constants.FirstLimit). // 只获取前两条记录
		Find()
	if err != nil {
		logger.Logger.Errorf("OrderService IsFirstPay err: %s", err.Error())
		return false
	}
	return len(orders) <= 1
}

// UpdateOrderBalance 更新订单余额
func (s *OrderService) UpdateOrderBalance(ctx context.Context, orderID string, saveAmt int32) error {
	order := store.QueryDB().AOrder
	orderCtx := order.WithContext(ctx)
	_, err := orderCtx.Where(order.OrderID.Eq(orderID)).UpdateSimple(order.SaveAmt.Value(saveAmt))
	if err != nil {
		logger.Logger.Errorf("OrderService UpdateOrderBalance err: %s", err.Error())
		return err
	}
	return nil
}

func (s *OrderService) ProcessOrder(ctx context.Context, openID, appID, token, secret, orderPlatform string, saveAmt int32) error {
	balanceCheckParam := &bean.DouyinBalanceParam{
		OpenID:      openID,
		AppID:       appID,
		Ts:          time.Now().Unix(),
		ZoneID:      "1",
		PF:          orderPlatform,
		AccessToken: token,
		MpSig:       secret,
	}

	return s.verifyPaymentCompletion(ctx, saveAmt, balanceCheckParam)
}

func (s *OrderService) verifyPaymentCompletion(ctx context.Context, orderSaveAmt int32, balanceCheckParam *bean.DouyinBalanceParam) error {
	startTime := time.Now()
	timeout := 2 * time.Minute // 增加超时时间到2分钟，考虑网络延迟

	// 首次查询余额作为基准
	initialBalance, err := s.https.GetGameBalance(ctx, balanceCheckParam)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "verifyPaymentCompletion 获取初始余额失败: %v", err)
		// 初始查询失败不直接返回，继续尝试
	}

	ticker := time.NewTicker(5 * time.Second) // 降低查询频率到5秒，减少服务器压力
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			if time.Since(startTime) > timeout {
				return errors.New("verifyPaymentCompletion 余额在2分钟内未发生变化，支付可能失败")
			}

			balance, err := s.https.GetGameBalance(ctx, balanceCheckParam)
			if err != nil {
				// 捕捉上下文取消错误
				if errors.Is(err, context.Canceled) {
					logger.Logger.WarnfCtx(ctx, "verifyPaymentCompletion 被取消")
					return err
				}
				logger.Logger.WarnfCtx(ctx, "verifyPaymentCompletion 获取游戏余额错误: %v. 继续重试...", err)
				continue // 重试逻辑
			}

			// 更严格的验证：检查余额是否确实增加
			if balance.SaveAmt > orderSaveAmt {
				// 如果有初始余额，进一步验证增加量
				if initialBalance != nil && (balance.SaveAmt-initialBalance.SaveAmt) <= 0 {
					logger.Logger.WarnfCtx(ctx, "verifyPaymentCompletion 余额未实际增加，可能是历史余额. initial: %d, current: %d",
						initialBalance.SaveAmt, balance.SaveAmt)
					continue
				}

				logger.Logger.InfofCtx(ctx, "verifyPaymentCompletion 支付验证成功. 当前余额: %d, 总余额: %d, 订单历史余额: %d",
					balance.Balance, balance.SaveAmt, orderSaveAmt)
				return nil
			}
		}
	}
}

// 查看订单是否存在
func (s *OrderService) CheckOrderExist(ctx context.Context, orderID string) (bool, error) {
	order := store.QueryDB().AOrder
	orderCtx := order.WithContext(ctx)
	orderInfo, err := orderCtx.Where(order.OrderID.Eq(orderID)).Where(order.IsDeleted.Zero()).First()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "OrderService CheckOrderExist err: %s", err.Error())
		return false, err
	}
	if orderInfo == nil {
		return false, nil
	}
	return true, nil
}

// GetTotalAmountByUserIDAndGameID 获取用户在特定游戏中的充值总金额
func (s *OrderService) GetTotalAmountByUserIDAndGameID(ctx context.Context, userID string, gameID string) (int64, error) {
	orderQuery := store.QueryDB().AOrder
	var result struct {
		TotalMoney int64
	}

	// 计算截止到当年4月30日23:59:59的时间戳
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "GetTotalAmountByUserIDAndGameID: failed to load location, error: %v", err)
		return 0, constants.ErrSystemServiceIsBusy
	}
	// 注意：这里固定使用2025年
	cutoffTime := time.Date(2025, time.April, 30, 23, 59, 59, 0, loc)
	cutoffTimestamp := cutoffTime.UnixMilli()

	// 记录查询的开始时间，用于性能监控（如需性能分析可使用此变量）
	// startTime := time.Now()

	err = orderQuery.WithContext(ctx).
		Select(orderQuery.Money.Sum().As("total_money")).
		Where(
			orderQuery.GameID.Eq(gameID),
			orderQuery.UserID.Eq(userID),
			orderQuery.Status.Eq(constants.PaymentProductShipmentSuccess), // 使用常量
			orderQuery.CreatedAt.Lte(cutoffTimestamp),                     // 增加时间限制
			orderQuery.IsDeleted.Zero(),                                   // 确保只统计未删除的订单
		).Scan(&result)

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "GetTotalAmountByUserIDAndGameID: failed to query total amount, userID: %s, gameID: %s, error: %v", userID, gameID, err)
		return 0, constants.ErrSystemServiceIsBusy
	}

	logger.Logger.InfofCtx(ctx, "GetTotalAmountByUserIDAndGameID: successfully queried total amount, userID: %s, gameID: %s, total: %d", userID, gameID, result.TotalMoney)
	return result.TotalMoney, nil
}

// GetLastWaitPayOrder 获取最后一个等待支付的订单
func (s *OrderService) GetLastWaitPayOrderByOpenID(ctx context.Context, openID string) (*model.AOrder, error) {
	user := store.QueryDB().AUserMinigame
	userCtx := user.WithContext(ctx)
	userInfo, err := userCtx.Where(user.OpenID.Eq(openID)).Where(user.IsDeleted.Zero()).First()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "OrderService GetLastWaitPayOrderByOpenID err: %s", err.Error())
		return nil, err
	}

	order := store.QueryDB().AOrder
	orderCtx := order.WithContext(ctx)
	orderInfo, err := orderCtx.Where(order.UserID.Eq(userInfo.UserID)).
		// Where(order.Status.Eq(constants.PaymentOrderWait)).
		Where(order.IsDeleted.Zero()).
		Order(order.CreatedAt.Desc()).First()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "OrderService GetLastWaitPayOrder err: %s", err.Error())
		return nil, err
	}
	return orderInfo, nil
}

// VerifyOrderLatest 验证订单是否是当前的最新订单
func (s *OrderService) VerifyOrderLatest(ctx context.Context, userID, goodsID, currentOrderID string) (bool, error) {
	order := store.QueryDB().AOrder
	orderCtx := order.WithContext(ctx)

	// Find the latest order for the user in current year
	latestOrders, err := orderCtx.Order(order.CreatedAt.Desc()).Where(
		order.UserID.Eq(userID),
		order.GoodsID.Eq(goodsID),
		order.IsDeleted.Zero(),
	).Find()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "OrderService VerifyOrderLatest err: %s", err.Error())
		return false, err
	}

	if len(latestOrders) == 0 {
		return false, nil
	}

	// currentOrderID == latestOrders[0].OrderID
	return latestOrders[0].OrderID == currentOrderID, nil
}

// TestOrder 测试订单
func (s *OrderService) TestOrder(ctx context.Context, req *bean.TestOrderReq) (*bean.Order, error) {
	goods := store.QueryDB().AGood
	goodsCtx := goods.WithContext(ctx)

	// 构建查询条件
	query := goodsCtx.Where(goods.GameID.Eq(req.GameID)).Where(goods.IsDeleted.Zero())
	if req.GoodsID != "" {
		query = query.Where(goods.GoodsID.Eq(req.GoodsID))
	} else {
		query = query.Order(goods.ID.Asc())
	}

	// 查询商品信息
	goodsInfo, err := query.First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("no valid goods found for game ID %s", req.GameID)
		}
		return nil, fmt.Errorf("failed to get goods info: %w", err)
	}

	// 创建订单
	return s.CreateTestOrder(ctx, &bean.CreateOrderReq{
		UserID:  req.UserID,
		GameID:  req.GameID,
		GoodsID: goodsInfo.GoodsID,
		Money:   goodsInfo.Money,
		Extra:   req.Extra,
	})
}

func (s *OrderService) CreateTestOrder(ctx context.Context, req *bean.CreateOrderReq) (*bean.Order, error) {
	goods := store.QueryDB().AGood
	goodsCtx := goods.WithContext(ctx)
	goodsInfo, err := goodsCtx.Where(goods.GameID.Eq(req.GameID)).Where(goods.GoodsID.Eq(req.GoodsID)).Where(goods.IsDeleted.Zero()).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, constants.ErrOrderGoodsInfo
	} else if err != nil {
		return nil, err
	}

	order := store.QueryDB().AOrder
	orderCtx := order.WithContext(ctx)
	orderModel := &model.AOrder{
		UserID:  req.UserID,
		GameID:  req.GameID,
		OrderID: util.UUIDWithoutHyphens(),
		GoodsID: req.GoodsID,
		Money:   req.Money,
		// PlatformType:     constants.PlatformTypeIOS, // 因测试平台类型强制为iOS
		// PayType:          constants.PayTypeIOSWechatH5Pay,
		PlatformType:     constants.PlatformTypeIOS, // 因测试平台类型强制为iOS
		PayType:          constants.PayTypeIOSDouyinPay,
		Status:           constants.PaymentOrderCreate,
		Extra:            req.Extra,
		ShipmentCallback: req.ShipmentCallback,
	}
	//if req.PayType == constants.PayTypeAndroidDouyinPay || req.PayType == constants.PayTypeIOSDouyinPay {
	//	if err := s.handleDouyinPay(orderModel, orderModel.Money); err != nil {
	//		return nil, err
	//	}
	//}
	err = orderCtx.Create(orderModel)
	if err != nil {
		return nil, err
	}
	orderRes := &bean.Order{}
	err = copier.Copy(&orderRes, orderModel)
	if err != nil {
		return nil, err
	}
	orderRes.GoodsName = goodsInfo.GoodsName
	return orderRes, nil
}

// NotifyFeishu 发送通知到飞书机器人
func (s *OrderService) NotifyFeishu(ctx context.Context, message string) error {
	// 构建请求体
	requestBody := map[string]interface{}{
		"msg_type": "text",
		"content": map[string]interface{}{
			"text": message,
		},
	}

	// 发送请求到飞书
	resp, err := s.client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(requestBody).
		Post(constants.FeishuWebhookURL)

	if err != nil {
		logger.Logger.Errorf("Failed to send Feishu notification: %v", err)
		return err
	}

	// 检查响应状态
	if resp.StatusCode() != http.StatusOK {
		err = fmt.Errorf("failed to send Feishu notification, status code: %d, response: %s", resp.StatusCode(), resp.String())
		logger.Logger.Errorf("%v", err)
		return err
	}

	logger.Logger.Infof("Successfully sent Feishu notification: %s", message)
	return nil
}
