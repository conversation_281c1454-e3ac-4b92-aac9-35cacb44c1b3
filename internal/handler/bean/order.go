package bean

import "git.panlonggame.com/bkxplatform/admin-console/internal/middleware"

type Order struct {
	ID               int32   `json:"id"`
	UserID           string  `json:"user_id"`    // 用户 id
	GoodsID          string  `json:"goods_id"`   // 商品 id
	OrderID          string  `json:"order_id"`   // 订单 id
	GoodsName        string  `json:"goods_name"` // 商品名称
	GameID           string  `json:"game_id"`
	Money            int32   `json:"money"`  // 金额
	Status           int32   `json:"status"` // 支付状态 1.待支付 2.支付成功 3.支付失败
	Extra            string  `json:"extra"`  // 额外信息
	PrepayID         string  `json:"prepay_id"`
	PayType          int32   `json:"pay_type"`
	GameCurrency     int32   `json:"game_currency"`
	TransactionsInfo string  `json:"transactions_info"`
	SaveAmt          int32   `json:"save_amt"`
	PlatformType     int32   `json:"platform_type"`
	GoodsPayTypes    []int32 `json:"goods_pay_types"`
	WechatProductID  string  `json:"wechat_product_id"`
	ShipmentCallback string  `json:"shipment_callback"` // 发货回调地址(非必填)
	CreatedAt        int64   `json:"created_at"`
	UpdatedAt        int64   `json:"updated_at"`
}

type CreateOrderReq struct {
	UserID  string `json:"user_id" binding:"required"`
	GameID  string `json:"game_id" binding:"required"`
	GoodsID string `json:"goods_id" binding:"required"`
	Money   int32  `json:"money"`
	// PlatformType int32  `json:"platform_type"` // 1 iOS 2 Android
	// PayType      int32  `json:"pay_type"`      // 支付类型 1:米大师 2:iOS H5支付  3:Google 4: iOS APP苹果支付
	// BuyQuantity int32  `json:"buy_quantity"`
	Extra string `json:"extra"`

	ShipmentCallback string `json:"shipment_callback"` // 发货回调地址
}

type GetOrderDetailSignReq struct {
	middleware.Header
	OrderID      string `form:"order_id"`
	PlatformType string `form:"platform_type"` // minigame douyin_minigame
}

type GetOrderDetailRes struct {
	OrderDetail *Order `json:"order_detail"`

	SignData  *WechatOrder `json:"wechat_sign_data,omitempty"`
	PaySig    string       `json:"pay_sig,omitempty"`
	Signature string       `json:"signature,omitempty"`

	DouyinOrder *DouyinOrder `json:"douyin_order,omitempty"`
}

type DouyinOrder struct {
	Mode         string `json:"mode,omitempty"`
	Env          int32  `json:"env,omitempty"`
	CurrencyType string `json:"currencyType"`
	Platform     string `json:"platform,omitempty"`
	BuyQuantity  int32  `json:"buyQuantity"`
	ZoneId       string `json:"zoneId"`
	CustomId     string `json:"customId"`
	ExtraInfo    string `json:"extraInfo"`
}

type SignData struct {
	OutTradeNo   string  `json:"out_trade_no"`
	GoodsPrice   float64 `json:"goods_price"`   // 对应数据库 money
	ProductID    string  `json:"product_id"`    // 对应数据库	goods_id
	Env          string  `json:"env"`           // 米大师正式环境 0 沙箱环境 1
	CurrencyType string  `json:"currency_type"` // 对应CNY 人民币
}

type GetOrderDetailCustomer struct{}

type WechatPayCallback struct{}

type WechatPayCallbackRes struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

// WechatH5PayCallbackReq
type WechatH5PayCallbackReq struct{}

// WechatMidasSignCallback
type WechatMidasSignCallbackReq struct {
	Signature string `form:"signature"`
	Timestamp string `form:"timestamp"`
	Nonce     string `form:"nonce"`
	Echostr   string `form:"echostr"`

	GameID string `form:"game_id"`
}

type WechatMidasCallbackReq struct {
	// query
	Signature    string `form:"signature"`
	Timestamp    string `form:"timestamp"`
	Nonce        string `form:"nonce"`
	EncryptType  string `form:"encrypt_type"`
	MsgSignature string `form:"msg_signature"`

	// body
	Encrypt    string `json:"Encrypt"`
	ToUserName string `json:"ToUserName"`

	GameID string `form:"game_id"`
}

type WechatMidas struct {
	ToUserName   string `json:"ToUserName"`
	FromUserName string `json:"FromUserName"`
	CreateTime   int64  `json:"CreateTime"`
	MsgType      string `json:"MsgType"`
	Event        string `json:"Event"`

	MiniGame WechatMidasMiniGame `json:"MiniGame"`
}

type WechatMidasMiniGame struct {
	Payload     string `json:"Payload"`
	PayEventSig string `json:"PayEventSig"`
	IsMock      bool   `json:"IsMock"`
}

type WechatMidasMiniGamePayload struct {
	OpenId     string                       `json:"OpenId"`
	Env        int32                        `json:"Env"`
	OutTradeNo string                       `json:"OutTradeNo"`
	GoodsInfo  WechatMidasMiniGameGoodsInfo `json:"GoodsInfo"`
}

type WechatMidasMiniGameGoodsInfo struct {
	ProductId   string `json:"ProductId"`
	Quantity    int32  `json:"Quantity"`
	ZoneId      string `json:"ZoneId"`
	OrigPrice   int32  `json:"OrigPrice"`
	ActualPrice int32  `json:"ActualPrice"`
	Attach      string `json:"Attach"`
	OrderSource int32  `json:"OrderSource"`
}

type WechatCustomerSignCallbackReq struct {
	Signature string `form:"signature"`
	Timestamp string `form:"timestamp"`
	Nonce     string `form:"nonce"`
	Echostr   string `form:"echostr"`

	GameID string `form:"game_id"`
}

// WechatCustomerCallbackReq 微信客户端回调参数
type WechatCustomerCallbackReq struct {
	ToUserName   string `json:"ToUserName"`
	FromUserName string `json:"FromUserName"`
	CreateTime   int64  `json:"CreateTime"`
	MsgType      string `json:"MsgType"`
	Content      string `json:"Content,omitempty"`
	MsgID        int64  `json:"MsgId,omitempty"`

	PagePath    string `json:"PagePath"`    // 文本 MsgType = “text” 小程序 MsgType = "miniprogrampage"
	SessionFrom string `json:"SessionFrom"` // 自定义数据，后期增加自定义使用
	// Encrypt string `json:"Encrypt"` // 加密数据，后期增加安全性使用

	PicUrl  string `json:"PicUrl"`  // 图片 MsgType = "image"
	MediaID string `json:"MediaId"` // 图片 MsgType = "image"

	Title        string `json:"Title"`        // 小程序 MsgType = "miniprogrampage"
	AppID        string `json:"AppId"`        // 小程序 MsgType = "miniprogrampage"
	ThumbUrl     string `json:"ThumbUrl"`     // 小程序 MsgType = "miniprogrampage"
	ThumbMediaID string `json:"ThumbMediaId"` // 小程序 MsgType = "miniprogrampage"

	Event    string   `json:"Event"` // 事件 MsgType = "event"
	MiniGame MiniGame `json:"MiniGame"`

	GameID string `json:"game_id"`
}

type MiniGame struct {
	OrderId      string  `json:"OrderId"`
	IsPreview    int     `json:"IsPreview"`
	ToUserOpenid string  `json:"ToUserOpenid"`
	Zone         int     `json:"Zone"`
	GiftTypeId   int     `json:"GiftTypeId"`
	GiftId       string  `json:"GiftId"`
	SendTime     int64   `json:"SendTime"`
	GoodsList    []Goods `json:"GoodsList"`
}

type Goods struct {
	Id  string `json:"Id"`
	Num int    `json:"Num"`
}

type Event struct {
	CreateTime int64    `json:"CreateTime"`
	MsgType    string   `json:"MsgType"`
	Event      string   `json:"Event"`
	MiniGame   MiniGame `json:"MiniGame"`
}

type CustomerSessionFrom struct {
	OrderID string `json:"order_id"`
}

type WechatCustomerCallbackRes struct{}

// GetOrderDetailCustomerReq 订单详情
type GetOrderDetailCustomerReq struct {
	middleware.Header

	OrderID      string `form:"order_id"`
	PlatformType string `form:"platform_type"` // minigame 、 douyin_minigame
}

// GetOrderDetailCustomerRes 订单详情&客服信息
type GetOrderDetailCustomerRes struct {
	OrderDetail         *Order               `json:"order_detail"`
	CustomerServiceData *CustomerServiceData `json:"customer_service_data,omitempty"`

	DouyinOrder *DouyinOrder `json:"douyin_order,omitempty"`
}

type CustomerServiceData struct {
	SendMessageTitle string `json:"send_message_title"`
	SendMessageImg   string `json:"send_message_img"`
	SendMessagePath  string `json:"send_message_path"`
}

// GetSubscribeConfigReq 获取订阅配置
type GetSubscribeConfigReq struct {
	URL string `form:"url"`
}

// GetOrderDetailH5PayReq H5支付
type GetOrderDetailH5PayReq struct {
	middleware.Header

	OrderID string `form:"order_id"`
	// OpenID  string `form:"open_id"`
	// URL     string `form:"url"`
	// PlatformType int32  `json:"platform_type"` // 1 iOS 2 Android
}

// GetOrderDetailH5PayRes
type GetOrderDetailH5PayRes struct {
	OrderDetail *Order `json:"order_detail"`

	WechatPayData *JSAPIWechatPayData `json:"wechat_pay_data"`

	// ConfigSign *ConfigSign `json:"config_sign"`
}

type ConfigSign struct {
	AppID     string `json:"appId"`
	TimeStamp string `json:"timeStamp"`
	NonceStr  string `json:"nonceStr"`
	Signature string `json:"signature"`
}

type JSAPIWechatPayData struct {
	AppID     string `json:"appId"`
	TimeStamp string `json:"timeStamp"`
	NonceStr  string `json:"nonceStr"`
	Package   string `json:"package"`
	SignType  string `json:"signType"`
	PaySign   string `json:"paySign"`

	PrepayID string `json:"prepay_id"`

	// Signature string `json:"signature"`
}

// PayTransactionsJSAPIRes JSAPI支付
type PayTransactionsJSAPIRes struct {
	PrepayID string `json:"prepay_id"`
}

// ProductShipment
type ProductShipmentRes struct {
	OrderID string `json:"order_id"`
	Code    int32  `json:"code"`
	Msg     string `json:"msg"`
}

type CallWithRetryOrder struct {
	OrderID string
	Err     error
}

type CallWithRetryOrderRes struct {
	OrderID string
	Err     error
}

// DouyinPaySignCallbackReq 抖音配置支付认证
type DouyinPaySignCallbackReq struct {
	Signature string `form:"signature"`
	Timestamp string `form:"timestamp"`
	Msg       string `form:"msg"`
	Nonce     string `form:"nonce"`
	EchoStr   string `form:"echostr"`

	GameID string
}

// DouyinPayCallbackReq 抖音支付
type DouyinPayCallbackReq struct {
	Signature string `json:"signature"`
	Timestamp string `json:"timestamp"`
	Msg       string `json:"msg"`
	Nonce     string `json:"nonce"`
	EchoStr   string `json:"echostr"`

	GameID string
}

type DouyinOrderSuccessPayInfo struct {
	AppID          string `json:"appid"`            // 小游戏appid
	CpOrderNo      string `json:"cp_orderno"`       // 开发者自定义订单号
	CpExtra        string `json:"cp_extra"`         // 开发者传的额外参数
	OrderNoChannel string `json:"order_no_channel"` // 小游戏后台交易单号
	AmountCent     int32  `json:"amount_cent"`      // 金额（分）
	AmountCoin     int32  `json:"amount_coin"`      // 金币
}

type DeductGameCurrencyParam struct {
	OpenID      string `json:"openid"`
	AppID       string `json:"appid"`
	Ts          int64  `json:"ts"`
	ZoneID      string `json:"zone_id"`
	PF          string `json:"pf"`
	Amt         int32  `json:"amt"`
	BillNo      string `json:"bill_no"`
	AccessToken string `json:"access_token"`
	MpSig       string `json:"mp_sig"`
}

// DouyinPaySuccessReq 抖音支付
// type DouyinPaySuccessReq struct {
// 	OrderID     string `json:"order_id"`
// 	BuyQuantity int32  `json:"buy_quantity"`
// }

// type DouyinPaySuccessRes struct {
// 	BillNo     string `json:"bill_no"`
// 	Balance    int    `json:"balance"`
// 	UsedGenAmt int    `json:"used_gen_amt"`
// }

type DouyinBalanceParam struct {
	OpenID      string `json:"openid"`
	AppID       string `json:"appid"`
	Ts          int64  `json:"ts"`
	ZoneID      string `json:"zone_id"`
	PF          string `json:"pf"`
	AccessToken string `json:"access_token"`
	MpSig       string `json:"mp_sig"`
}

type OrderReq struct {
	Attempt       int                   `json:"attempt"` // 试图充实
	Order         *ProductShipmentOrder `json:"order"`
	CallbackURL   string                `json:"callback_url"`
	PlatformAppID string                `json:"platform_app_id"`
}

type ProductShipmentOrder struct {
	ID                      int32  `json:"id"`
	UserID                  string `json:"user_id"`
	OrderID                 string `json:"order_id"` // 订单 id
	GameID                  string `json:"game_id"`
	GoodsID                 string `json:"goods_id"`       // 商品 ID
	PayType                 int32  `json:"pay_type"`       // 支付类型 1:微信安卓米大师 2:iOS H5支付 3:Google 4: iOS APP苹果支付 5: 抖音安卓虚拟支付 6: 抖音iOS虚拟支付
	Money                   int32  `json:"money"`          // 金额 (分)
	CurrencyPrice           int32  `json:"currency_price"` // 实际支付金额
	PlatformType            int32  `json:"platform_type"`  // 平台类型 1 iOS 2 安卓
	Status                  int32  `json:"status"`         // 支付状态 1.创建订单 2.待支付 3.支付成功(微信)  4.发货成功(回调平台)  5.发货支付失败
	Extra                   string `json:"extra"`          // 额外信息
	PayerOpenID             string `json:"payer_open_id"`  // 支付的open_id
	GameCurrency            int32  `json:"game_currency"`  // 抖音平台购买的游戏币数量, 微信平台为0
	PrepayID                string `json:"prepay_id"`      // 微信
	ThirdPartyTransactionID string `json:"third_party_transaction_id"`
	TransactionsInfo        string `json:"transactions_info"`
	CallbackOriginData      string `json:"callback_origin_data"` // 微信回调支付原始数据信息
	SaveAmt                 int32  `json:"save_amt"`             // 抖音历史累计充值游戏币数量
	CreatedAt               int64  `json:"created_at"`
	UpdatedAt               int64  `json:"updated_at"`
	// IsDeleted               bool   `json:"is_deleted"`
}

// GetOrderPayURLReq 获取用户支付链接(ios)
type GetOrderPayURLReq struct {
	middleware.Header

	OrderID string `json:"order_id"`
}

type GetOrderPayURLResp struct {
	PayURL string `json:"pay_url"`
}

// {
//   role_id: "",
//   player_id: 'xx',
//   player_name: 'xx',
//   player_level: 'xx',
//   recharge_total_amount: 'xx',
//   zone: 'xx',
//   custom_data: 'xxx'
// }

type UserSessionFrom struct {
	RoleID              string                 `json:"role_id"`
	PlayerID            string                 `json:"player_id"`
	PlayerName          string                 `json:"player_name"`
	PlayerLevel         int32                  `json:"player_level"`
	RechargeTotalAmount int32                  `json:"recharge_total_amount"`
	Zone                string                 `json:"zone"`
	CustomData          map[string]interface{} `json:"custom_data"`

	// 是否关闭机器人欢迎语
	CloseRobotWelcome bool `json:"close_robot_welcome"`
}

// // GetOrderDetailH5Req H5订单详情请求
// type GetOrderDetailH5Req struct {
// 	middleware.Header
// 	OrderID string `form:"order_id" binding:"required"` // 订单ID
// }

// GetOrderDetailH5Res H5订单详情响应
type GetOrderDetailH5Res struct {
	OrderDetail *Order `json:"order_detail"` // 订单详情
}

// TestOrderReq 测试订单请求
type TestOrderReq struct {
	middleware.Header
	// UserID  string `json:"user_id"`  // 用户ID
	// GameID  string `json:"game_id"`  // 游戏ID
	// Money   int32  `json:"money"`    // 金额
	GoodsID string `json:"goods_id"` // 商品ID
	Extra   string `json:"extra"`    // 额外信息
}

// GetOrderPayQRCodeReq 获取订单支付二维码请求
type GetOrderPayQRCodeReq struct {
	middleware.Header
	OrderID string `json:"order_id"`
}

// GetOrderPayQRCodeResp 获取订单支付二维码响应
type GetOrderPayQRCodeResp struct {
	QRCodeBase64 string `json:"qr_code_base64"` // 二维码图片的base64编码
	OrderDetail  *Order `json:"order_detail"`   // 订单详情
}

// GetOrderTotalReq 定义了获取订单总额接口的请求参数
type GetOrderTotalReq struct {
	GameID string `json:"game_id" binding:"required"` // 游戏ID
	UserID string `json:"user_id" binding:"required"` // 用户ID
}

// GetOrderTotalResp 定义了获取订单总额接口的响应数据
type GetOrderTotalResp struct {
	TotalAmount int64 `json:"total_amount"` // 充值总金额
}
