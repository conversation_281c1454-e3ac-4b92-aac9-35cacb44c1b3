package bean

type DouyinParam struct {
	AppID     string `json:"app_id"`
	AppSecret string `json:"app_secret"`
}

type DouyinResp struct {
	DouyinError
	Error           int    `json:"error"`
	SessionKey      string `json:"session_key"`
	OpenID          string `json:"openid"`
	AnonymousOpenID string `json:"anonymous_openid"`
	UnionID         string `json:"unionid"`
}

type DouyinError struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
	Error   int    `json:"error"`
	Message string `json:"message"`
}

type DouyinUser struct {
	SessionKey      string `json:"session_key"`
	OpenID          string `json:"openid"`
	AnonymousOpenID string `json:"anonymous_openid"`
	UnionID         string `json:"unionid"`
}

type DouyinWallet struct {
	ErrCode    int    `json:"errcode"`
	ErrMsg     string `json:"errmsg"`
	BillNo     string `json:"bill_no"`
	Balance    int    `json:"balance"`
	UsedGenAmt int    `json:"used_gen_amt"`
}

// DouyinAccessToken
type DouyinAccessToken struct {
	ErrNo   int    `json:"err_no"`
	ErrTips string `json:"err_tips"`
	Data    struct {
		AccessToken string `json:"access_token"`
		ExpiresIn   int    `json:"expires_in"`
	} `json:"data"`
}

// DouyinBalance 抖音游戏币余额
type DouyinBalance struct {
	DouyinError
	Balance    int32 `json:"balance"`
	GenBalance int32 `json:"gen_balance"`
	PresentSum int32 `json:"present_sum"`
	SaveAmt    int32 `json:"save_amt"` // 历史累计充值游戏币数量
	SaveSum    int32 `json:"save_sum"`
}

// DouyinGenerateRoomIDResp 生成直播间ID
type DouyinGenerateRoomIDResp struct {
	Code int `json:"code"`
	Data struct {
		ChannelID string `json:"channelId"`
	} `json:"data"`
	Msg string `json:"msg"`
}

// DouyinCustomerServiceCallbackReq 抖音客服推送回调请求
type DouyinCustomerServiceCallbackReq struct {
	AppID          string `json:"app_id"`                  // 小游戏ID
	ConversationID int64  `json:"conversation_id"`         // 会话ID
	MsgID          int64  `json:"msg_id"`                  // 消息ID
	CreateTime     int64  `json:"create_time"`             // 用户消息的创建时间，秒级时间戳
	MsgType        string `json:"msg_type"`                // 消息类型：text、image、video、score
	OpenID         string `json:"open_id"`                 // 用户的 open_id
	Content        string `json:"content,omitempty"`       // 文本消息内容（msg_type = "text" 时返回）
	PicURL         string `json:"pic_url,omitempty"`       // 图片 url（msg_type = "image" 时返回）
	VideoURL       string `json:"video_url,omitempty"`     // 视频 url（msg_type = "video" 时返回）
	ScoreContent   string `json:"score_content,omitempty"` // 评分消息内容（msg_type = "score" 时返回）

	// 兼容旧字段
	GameID      string `json:"game_id"`      // 游戏ID
	UserID      string `json:"user_id"`      // 用户 uniqueID
	SessionFrom string `json:"session_from"` // 用户进入客服消息来源
	Event       string `json:"event"`        // 事件类型
	Body        any    `json:"body"`         // 卡片数据对象，当msg_type为 miniprogrampage 时，该不为空
	Timestamp   int64  `json:"timestamp"`    // 时间戳
}

// DouyinCustomerServiceCallbackResp 抖音客服推送回调响应
// 根据抖音官方文档，响应格式应该是 success/err_code/reason 而不是 code/msg
type DouyinCustomerServiceCallbackResp struct {
	Success bool   `json:"success"`            // 是否成功
	ErrCode *int32 `json:"err_code,omitempty"` // 错误码（仅在失败时返回）
	Reason  string `json:"reason,omitempty"`   // 失败原因（仅在失败时返回）
}

// DouyinPushCustomerServiceMessageReq 抖音客服推送消息请求
type DouyinPushCustomerServiceMessageReq struct {
	GameID   string   `json:"game_id"`
	UserIDs  []string `json:"user_ids"`  // 用户 uniqueID，限最多一次发送 100 个
	PushType string   `json:"push_type"` // 推送类型
	MsgType  string   `json:"msg_type"`  // text、image、miniprogrampage、link
	TaskName string   `json:"task_name"` // 任务名称，用于更新延迟推送消息
	Delay    int32    `json:"delay"`     // 延时秒数，如果type是 delayed_push时，必须大于0

	Content  string `json:"content"`   // 消息内容 text类型
	Title    string `json:"title"`     // 小程序卡片标题
	PagePath string `json:"page_path"` // 小程序卡片路径
	ImgUrl   string `json:"img_url"`   // image和小程序卡片类型图片

	LinkTitle    string `json:"link_title"`     // 链接消息的标题
	LinkDesc     string `json:"link_desc"`      // 链接消息的描述
	LinkUrl      string `json:"link_url"`       // 链接消息的跳转地址
	LinkThumbURL string `json:"link_thumb_url"` // 链接消息的图片地址
}

// DouyinPushCustomerServiceMessageResp 抖音客服推送消息响应
type DouyinPushCustomerServiceMessageResp struct {
	SuccessCount int32 `json:"success_count"`
}

// DouyinGiftDeliveryReq 抖音游戏站礼包推送请求
type DouyinGiftDeliveryReq struct {
	GiftID   int64            `json:"gift_id"`   // 发货时对应的礼包ID，正式请求一般有，在道具测试阶段无
	GiftCode string           `json:"gift_code"` // 此次发货使用的礼包码，可用于消息去重
	OpenID   string           `json:"open_id"`   // 领奖用户的 open_id
	PropList []DouyinGiftProp `json:"prop_list"` // 此次发货的道具列表
	EnvType  string           `json:"env_type"`  // 发货请求的环境标识：development、production
	SendTime int64            `json:"send_time"` // 礼包推送请求触发的秒级时间戳
	GameID   string           `json:"game_id"`   // 游戏ID（内部使用）
}

// DouyinGiftProp 抖音礼包道具
type DouyinGiftProp struct {
	PropID string `json:"prop_id"` // 道具ID
	Name   string `json:"name"`    // 道具名称
	Count  int64  `json:"count"`   // 发货数量
}

// DouyinGiftDeliveryTaskReq 抖音礼包推送异步任务请求
type DouyinGiftDeliveryTaskReq struct {
	CallbackURL  string                         `json:"callback_url"`  // 回调地址
	GameID       string                         `json:"game_id"`       // 游戏ID
	CallbackData *DouyinGiftDeliveryCallbackReq `json:"callback_data"` // 回调数据（转换后的数据，用于发送给游戏服务器）
	Attempt      int                            `json:"attempt"`       // 重试次数
}

// DouyinGiftDeliveryCallbackReq 抖音礼包推送回调请求（发送给游戏服务器的数据结构）
type DouyinGiftDeliveryCallbackReq struct {
	GiftID   int64            `json:"gift_id"`   // 发货时对应的礼包ID，正式请求一般有，在道具测试阶段无
	GiftCode string           `json:"gift_code"` // 此次发货使用的礼包码，可用于消息去重
	UserID   string           `json:"user_id"`   // 领奖用户的 user_id（从 open_id 转换而来）
	PropList []DouyinGiftProp `json:"prop_list"` // 此次发货的道具列表
	EnvType  string           `json:"env_type"`  // 发货请求的环境标识：development、production
	SendTime int64            `json:"send_time"` // 礼包推送请求触发的秒级时间戳
	GameID   string           `json:"game_id"`   // 游戏ID（内部使用）
}
